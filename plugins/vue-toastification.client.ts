import Toast, { POSITION } from "vue-toastification"
import "vue-toastification/dist/index.css"

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.use(Toast, {
    // Cấu hình toast
    position: POSITION.TOP_RIGHT,
    timeout: 3000,
    closeOnClick: true,
    pauseOnFocusLoss: true,
    pauseOnHover: true,
    draggable: true,
    draggablePercent: 0.6,
    showCloseButtonOnHover: false,
    hideProgressBar: false,
    closeButton: "button",
    icon: true,
    rtl: false,
    transition: "Vue-Toastification__bounce",
    maxToasts: 5,
    newestOnTop: true,
    
    // Custom styling
    toastDefaults: {
      [Toast.TYPE.SUCCESS]: {
        timeout: 3000,
        hideProgressBar: false,
      },
      [Toast.TYPE.ERROR]: {
        timeout: 5000,
        hideProgressBar: false,
      },
      [Toast.TYPE.INFO]: {
        timeout: 3000,
        hideProgressBar: false,
      },
      [Toast.TYPE.WARNING]: {
        timeout: 4000,
        hideProgressBar: false,
      }
    }
  })
})
