# Trang chủ Bảo hiểm BIC

## Mô tả
Trang chủ được tạo từ thiết kế Figma cho sản phẩm Bảo hiểm Bắt buộc trách nhiệm dân sự của xe cơ giới.

## Tính năng đã triển khai

### 1. Thiết kế UI
- ✅ Header với logo và menu điều hướng
- ✅ Banner chính với hình ảnh sản phẩm
- ✅ Nội dung chính với thông tin sản phẩm:
  - Tiêu đề sản phẩm với typography đặc biệt
  - Thời hạn bảo hiểm: 30 ngày
  - Quyền lợi: 150 triệu đồng/người, 100 triệu đồng/tài sản
  - <PERSON><PERSON> bảo hiểm: 293,333 đ
- ✅ Các nút hành động: "Mua bảo hiểm" và "Xem chi tiết"
- ✅ Footer với thông tin bản quyền
- ✅ Các element trang trí (background shapes)

### 2. Hình ảnh
- ✅ Tất cả hình ảnh đã được download từ Figma
- ✅ Đổi tên file thành dạng rút gọn dễ hiểu
- ✅ Thay đổi localhost thành 127.0.0.1 trong script download
- ✅ Lưu trữ trong thư mục `public/assets/images/`

### 3. Responsive Design
- ✅ Thiết kế responsive cơ bản
- ✅ Tối ưu cho desktop (1920px)
- ✅ Media queries cho tablet và mobile

### 4. SEO
- ✅ Meta tags cơ bản
- ✅ Open Graph tags
- ✅ Title và description tối ưu

## Cấu trúc file

```
├── pages/
│   └── index.vue                 # Trang chủ chính
├── public/assets/images/         # Hình ảnh tĩnh
│   ├── banner1.png              # Banner chính
│   ├── logo.svg                 # Logo BIC
│   ├── image1.png               # Hình ảnh phụ 1
│   ├── image2.png               # Hình ảnh phụ 2
│   ├── icon-time.svg            # Icon thời gian
│   ├── icon-list.svg            # Icon danh sách
│   ├── group.svg                # Icon quyền lợi
│   ├── group1.svg               # Icon quyền lợi (fill)
│   ├── vector7-stroke.svg       # Đường gạch dưới
│   └── rectangle*.svg           # Các shape trang trí
├── scripts/
│   └── download-images.js       # Script download hình ảnh
└── utils/
    └── imageConstants.ts        # Constants cho đường dẫn hình ảnh
```

## Hướng dẫn chạy

1. **Cài đặt dependencies:**
   ```bash
   npm install
   ```

2. **Chạy development server:**
   ```bash
   npm run dev
   ```

3. **Truy cập ứng dụng:**
   - Mở trình duyệt và truy cập: http://localhost:3000
   - Nếu port 3000 bị chiếm, Nuxt sẽ tự động chuyển sang port khác

## Script download hình ảnh

Để download lại hình ảnh từ Figma (nếu cần):

```bash
node scripts/download-images.js
```

Script này sẽ:
- Download tất cả hình ảnh từ localhost:3845 (thay đổi thành 127.0.0.1)
- Đổi tên file thành dạng rút gọn
- Lưu vào thư mục `assets/images/`
- Tạo file constants trong `utils/imageConstants.ts`

## Công nghệ sử dụng

- **Framework:** Nuxt.js 3
- **Styling:** Tailwind CSS
- **TypeScript:** Hỗ trợ đầy đủ
- **Build tool:** Vite
- **Package manager:** npm

## Tối ưu hóa

### Performance
- Lazy loading cho hình ảnh
- Tối ưu kích thước bundle
- Preload các resource quan trọng

### SEO
- Meta tags đầy đủ
- Structured data (có thể thêm)
- Sitemap (có thể thêm)

### Accessibility
- Alt text cho hình ảnh
- Semantic HTML
- Keyboard navigation (có thể cải thiện)

## Ghi chú kỹ thuật

1. **Font loading:** Sử dụng Google Fonts cho Helvetica Neue với fallback
2. **Image optimization:** Hình ảnh được serve từ thư mục public
3. **CSS:** Sử dụng Tailwind với custom styles cho các element đặc biệt
4. **Responsive:** Breakpoints tại 1920px và 1200px

## Phát triển tiếp theo

- [ ] Thêm animation và transitions
- [ ] Tối ưu responsive cho mobile
- [ ] Thêm dark mode
- [ ] Tích hợp API backend
- [ ] Thêm form mua bảo hiểm
- [ ] Thêm trang chi tiết sản phẩm
- [ ] Testing (unit tests, e2e tests)
