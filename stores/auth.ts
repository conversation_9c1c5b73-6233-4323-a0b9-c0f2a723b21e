import { defineStore } from 'pinia'

interface User {
  id?: string
  email: string
  fullName?: string
  avatar?: string
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: false
  }),

  getters: {
    currentUser: (state) => state.user,
    isLoggedIn: (state) => state.isAuthenticated && !!state.user,
    userEmail: (state) => state.user?.email || '',
    userFullName: (state) => state.user?.fullName || '',
  },

  actions: {
    // Initialize auth state from localStorage
    initAuth() {
      if (import.meta.client) {
        const token = localStorage.getItem('auth_token')
        const user = localStorage.getItem('auth_user')

        if (token && user) {
          try {
            this.token = token
            this.user = JSON.parse(user)
            this.isAuthenticated = true
          } catch (error) {
            console.error('Error parsing stored user data:', error)
            this.clearAuth()
          }
        }
      }
    },

    // Login action
    async login(credentials: { email: string; password: string }) {
      this.isLoading = true
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Mock validation - replace with actual API call
        const mockValidCredentials = {
          email: '<EMAIL>',
          password: 'password123'
        }
        
        if (credentials.email === mockValidCredentials.email && 
            credentials.password === mockValidCredentials.password) {
          
          // Mock user data and token
          const userData: User = {
            id: '1',
            email: credentials.email,
            fullName: 'Người dùng Test',
            avatar: ''
          }
          
          const token = 'mock_jwt_token_' + Date.now()
          
          // Update store state
          this.user = userData
          this.token = token
          this.isAuthenticated = true
          
          // Store in localStorage
          if (import.meta.client) {
            localStorage.setItem('auth_token', token)
            localStorage.setItem('auth_user', JSON.stringify(userData))
          }
          
          return { success: true, user: userData }
        } else {
          throw new Error('Email hoặc mật khẩu không đúng.')
        }
      } catch (error) {
        this.clearAuth()
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Register action
    async register(userData: { fullName: string; email: string; password: string }) {
      this.isLoading = true
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        // Mock successful registration
        const newUser: User = {
          id: Date.now().toString(),
          email: userData.email,
          fullName: userData.fullName,
          avatar: ''
        }
        
        return { success: true, user: newUser }
      } catch (error) {
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Logout action
    logout() {
      this.clearAuth()
      
      // Redirect to home page
      if (import.meta.client) {
        navigateTo('/')
      }
    },

    // Clear authentication data
    clearAuth() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      
      if (import.meta.client) {
        localStorage.removeItem('auth_token')
        localStorage.removeItem('auth_user')
      }
    },

    // Update user profile
    updateProfile(userData: Partial<User>) {
      if (this.user) {
        this.user = { ...this.user, ...userData }
        
        if (import.meta.client) {
          localStorage.setItem('auth_user', JSON.stringify(this.user))
        }
      }
    },

    // Check if user is authenticated (for route guards)
    checkAuth(): boolean {
      return this.isAuthenticated && !!this.user
    },

    // Forgot password action
    async forgotPassword(email: string) {
      this.isLoading = true
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        // Mock successful password reset request
        return { success: true, message: 'Liên kết đặt lại mật khẩu đã được gửi đến email của bạn!' }
      } catch (error) {
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Change password action
    async changePassword(data: { currentPassword: string; newPassword: string }) {
      this.isLoading = true
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Mock successful password change
        return { success: true, message: 'Đổi mật khẩu thành công!' }
      } catch (error) {
        throw error
      } finally {
        this.isLoading = false
      }
    }
  }
})


