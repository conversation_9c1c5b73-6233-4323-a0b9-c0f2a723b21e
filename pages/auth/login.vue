<template>
  <div class="flex items-center justify-center mt-16">
    <div class="w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md">
      <h1 class="text-2xl font-bold text-center text-gray-800"><PERSON><PERSON><PERSON> nhập</h1>
      
      <Form class="space-y-4" @submit="onSubmit" :validation-schema="validationSchema" v-slot="{ errors }">
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
          <Field
            id="email"
            name="email"
            type="email"
            :class="[
              'mt-1 block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none sm:text-sm',
              errors.email 
                ? 'border-red-500 focus:border-red-500 focus:ring-red-500' 
                : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
            ]"
            placeholder="<EMAIL>"
          />
          <div class="h-5">
            <ErrorMessage name="email" class="mt-1 text-xs text-red-600" />
          </div>
        </div>

        <div>
          <label for="password" class="block text-sm font-medium text-gray-700">Mật khẩu</label>
          <Field
            id="password"
            name="password"
            type="password"
            :class="[
              'mt-1 block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none sm:text-sm',
              errors.password 
                ? 'border-red-500 focus:border-red-500 focus:ring-red-500' 
                : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
            ]"
          />
          <div class="h-5">
            <ErrorMessage name="password" class="mt-1 text-xs text-red-600" />
          </div>
        </div>

        <button 
          type="submit"
          class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Đăng nhập
        </button>
      </Form>

      <p class="text-sm text-center text-gray-600">
        Chưa có tài khoản? 
        <NuxtLink to="/auth/register" class="font-medium text-blue-600 hover:text-blue-500">Đăng ký ngay</NuxtLink>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Form, Field, ErrorMessage } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import * as z from 'zod';

useHead({
  title: 'Đăng nhập'
})

// Định nghĩa schema validation bằng Zod
const validationSchema = toTypedSchema(
  z.object({
    email: z.string().nonempty('Email là bắt buộc').email('Email không hợp lệ'),
    password: z.string().nonempty('Mật khẩu là bắt buộc').min(8, 'Mật khẩu phải có ít nhất 8 ký tự'),
  })
);

function onSubmit(values: any) {
  // Logic xử lý đăng nhập ở đây
  // Dữ liệu đã được validate, bạn có thể gửi nó tới API
  console.log('Đã validate thành công:', values);
  alert(JSON.stringify(values, null, 2));
  // Ví dụ gọi API:
  // const { data, error } = await useApi('/login', {
  //   method: 'POST',
  //   body: values
  // })
}
</script>
