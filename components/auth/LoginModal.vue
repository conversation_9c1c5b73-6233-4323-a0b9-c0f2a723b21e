<template>
  <BaseModal
    :is-open="isOpen"
    title="Đăng nhập"
    size="lg"
    body-class="w-[452px] mx-auto"
    @close="closeModal"
    @open="onModalOpen"
  >
    <form @submit="onFormSubmit" class="space-y-6">
      <div class="space-y-6">
        <!-- Email Field -->
        <div class="flex flex-col gap-3">
          <label class="text-black text-base font-bold leading-[1.2]">
            Email
          </label>
          <div class="relative">
            <Field
              name="email"
              type="email"
              :class="[
                'w-full h-[54px] px-[14px] border rounded-lg text-base font-normal leading-[1.2] placeholder-[#919EAB] focus:outline-none',
                errors.email ? 'border-red-500 focus:border-red-500' : 'border-[#E9ECEE] focus:border-[#0066B3]'
              ]"
              placeholder="Nhập Email"
            />
          </div>
          <ErrorMessage name="email" class="text-xs text-red-600 min-h-[16px]" />
        </div>

        <!-- Password Field -->
        <div class="flex flex-col gap-3">
          <label class="text-black text-base font-bold leading-[1.2]">
            Mật khẩu
          </label>
          <div class="relative">
            <Field
              name="password"
              :type="showPassword ? 'text' : 'password'"
              :class="[
                'w-full h-[54px] px-[14px] pr-[50px] border rounded-lg text-base font-normal leading-[1.2] placeholder-[#919EAB] focus:outline-none',
                errors.password ? 'border-red-500 focus:border-red-500' : 'border-[#E9ECEE] focus:border-[#0066B3]'
              ]"
              placeholder="Nhập mật khẩu"
            />
            <!-- Eye Icon with proper padding -->
            <button
              type="button"
              @click="togglePasswordVisibility"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <svg v-if="!showPassword" class="w-5 h-5 text-[#919EAB]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
              <svg v-else class="w-5 h-5 text-[#919EAB]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
              </svg>
            </button>
          </div>
          <ErrorMessage name="password" class="text-xs text-red-600 min-h-[16px]" />
        </div>

        <!-- Login Button -->
        <button
          type="submit"
          :disabled="isLoading"
          class="w-full bg-[#0D68B2] hover:bg-[#0056a3] disabled:opacity-50 disabled:cursor-not-allowed text-white font-bold text-xl leading-[1.22] h-[52px] rounded-[14.613px] shadow-[0px_10.96px_36.532px_0px_rgba(0,0,0,0.15)] transition-colors flex items-center justify-center gap-3"
        >
          <span v-if="isLoading" class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></span>
          <span class="text-[#F6F6F6] text-xl font-bold leading-[1.22] text-center">
            {{ isLoading ? 'Đang xử lý...' : 'Đăng nhập' }}
          </span>
        </button>
      </div>

      <!-- Links -->
      <div class="flex justify-between items-center text-base">
        <div class="text-black">
          <span>Chưa có tài khoản? </span>
          <button type="button" @click="switchToRegister" class="text-[#4F63EE] hover:underline">
            Đăng ký
          </button>
        </div>
        <button type="button" @click="switchToForgotPassword" class="text-[#4F63EE] hover:underline">
          Quên mật khẩu?
        </button>
      </div>
    </form>

    <!-- Toast Notifications -->
    <div v-if="showToast" class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-[60] transition-all duration-300">
      {{ toastMessage }}
    </div>
    <div v-if="showError" class="fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-[60] transition-all duration-300">
      {{ errorMessage }}
    </div>
  </BaseModal>
</template>

<script setup lang="ts">
import { Field, ErrorMessage, useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import BaseModal from '../BaseModal.vue'
import { useAuthStore } from '~/stores/auth'

interface Props {
  isOpen: boolean
}

interface Emits {
  close: []
  switchToRegister: []
  switchToForgotPassword: []
  loginSuccess: [user: any]
}

defineProps<Props>()
const emit = defineEmits<Emits>()

// Reactive variables
const isLoading = ref(false)
const showPassword = ref(false)
const showToast = ref(false)
const toastMessage = ref('')
const showError = ref(false)
const errorMessage = ref('')
const passwordVisibilityTimer = ref<NodeJS.Timeout | null>(null)

// Zod schema
const zodSchema = z.object({
  email: z.string().min(1, 'Email là bắt buộc').email('Email không hợp lệ'),
  password: z.string().min(1, 'Mật khẩu là bắt buộc'),
})

type FormData = z.infer<typeof zodSchema>

// Form setup
const { handleSubmit, errors, resetForm } = useForm({
  validationSchema: toTypedSchema(zodSchema),
  initialValues: {
    email: '',
    password: ''
  }
})

// Toggle password visibility with 5-second auto-hide
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
  
  if (passwordVisibilityTimer.value) {
    clearTimeout(passwordVisibilityTimer.value)
  }
  
  if (showPassword.value) {
    passwordVisibilityTimer.value = setTimeout(() => {
      showPassword.value = false
      passwordVisibilityTimer.value = null
    }, 5000)
  }
}

// Notifications
const showToastNotification = (message: string) => {
  toastMessage.value = message
  showToast.value = true
  setTimeout(() => {
    showToast.value = false
  }, 3000)
}

const showErrorNotification = (message: string) => {
  errorMessage.value = message
  showError.value = true
  setTimeout(() => {
    showError.value = false
  }, 3000)
}

// Modal handlers
const closeModal = () => {
  emit('close')
  resetForm()
}

const onModalOpen = () => {
  resetForm()
}

const switchToRegister = () => {
  emit('switchToRegister')
}

const switchToForgotPassword = () => {
  emit('switchToForgotPassword')
}

// Form submission
const onSubmit = async (values: FormData) => {
  isLoading.value = true

  try {
    const authStore = useAuthStore()
    const result = await authStore.login(values)

    if (result.success) {
      showToastNotification('Đăng nhập thành công!')

      setTimeout(() => {
        emit('loginSuccess', result.user)
        closeModal()
      }, 1500)
    }
  } catch (error: any) {
    showErrorNotification(error.message || 'Có lỗi xảy ra. Vui lòng thử lại.')
  } finally {
    isLoading.value = false
  }
}

const onFormSubmit = handleSubmit(onSubmit)

// Cleanup
onUnmounted(() => {
  if (passwordVisibilityTimer.value) {
    clearTimeout(passwordVisibilityTimer.value)
  }
})
</script>
